<table name="user" namespace="test.orm">
    <columns>
        <column name="id" type="int64" desc="唯一主键" auto_increment="true"/>
        <column name="name" type="string" desc="名称" length="30"/>
        <column name="email" type="string" default="<EMAIL>"/>
        <column name="phone" type="string"/>
        <column name="status" type="int32" default="10"/>
        <column name="create_time" type="timestamp"/>
        <column name="update_time" type="timestamp" default="current_timestamp" update="current_timestamp"/>
    </columns>
    <indexs>
        <index name="pk" cols="id" type="pk" desc="主键"/>
        <index name="name" cols="name" type="uniq" desc="关联"/>
        <index name="email" cols="email" type="uniq" desc="关联"/>
        <index name="status" cols="status" type="index" desc="关联"/>
    </indexs>
</table>
