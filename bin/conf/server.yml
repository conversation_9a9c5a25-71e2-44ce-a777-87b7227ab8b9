servers:
    - address: ["0.0.0.0:8090", "127.0.0.1:8091", "/tmp/test.sock"]
      keepalive: 1
      timeout: 1000
      name: sylar/1.1
      accept_worker: accept
      io_worker: http_io
      process_worker:  http_io
      type: http
    - address: ["0.0.0.0:8062", "0.0.0.0:8061"]
      timeout: 1000
      name: sylar-rock/1.0
      accept_worker: accept
      io_worker: io
      process_worker:  io
      type: rock
