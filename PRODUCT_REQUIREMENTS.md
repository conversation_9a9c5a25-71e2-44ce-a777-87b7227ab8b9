# Sylar 框架产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目简介

Sylar 是一个基于 C++11 开发的高性能、模块化的分布式服务器框架。它以协程为核心，提供了包括日志、配置、线程管理、IO调度、HTTP服务、数据库集成在内的一整套解决方案。Sylar 旨在帮助开发者快速构建稳定、高效、可扩展的后台服务。

通过对底层Socket API进行Hook，Sylar能够以同步的方式编写异步代码，极大地简化了高并发业务的逻辑开发复杂度。框架设计遵循"约定优于配置"的原则，力求提供简洁易用的API，同时保持高度的灵活性和可定制性。

### 1.2 核心特性

*   **轻量级协程**：基于 `ucontext_t` 实现的非对称协程，提供高效的上下文切换能力。
*   **N-M 协程调度**：将 M 个协程调度到 N 个线程上执行，充分利用多核CPU性能。
*   **事件驱动的IO调度**：基于 `epoll` 实现高并发网络IO处理，并集成高精度定时器。
*   **全面的Hook机制**：透明地将同步的系统调用（如 `socket`, `sleep`）转换为异步IO，对业务代码无侵入。
*   **模块化设计**：提供日志、配置、数据库（MySQL/Redis/SQLite）、HTTP/WebSocket、服务发现（Zookeeper）等一系列可独立或组合使用的模块。
*   **高性能HTTP协议栈**：使用 `Ragel` 有限状态机解析HTTP/1.1协议，性能卓越。
*   **灵活的Servlet接口**：借鉴Java Servlet思想，轻松实现复杂的HTTP业务逻辑。
*   **丰富的数据处理能力**：内置 `ByteArray` 序列化、`Protobuf` 集成、JSON/YAML配置解析等。

### 1.3 整体架构

Sylar 框架采用分层和模块化的设计思想，其核心架构可以概括为下图：

```mermaid
graph TD
    A[Application Layer] --> B{Sylar Core Modules};
    
    subgraph B
        direction LR
        
        subgraph B1 [Protocols & Applications]
            HTTP;
            WebSocket;
            Rock[Rock Protocol];
            NS[Service Discovery];
        end

        subgraph B2 [Core Scheduling & Encapsulation]
            Hook;
            IOManager[IO/Fiber Scheduler];
            Scheduler[Fiber Scheduler];
            Fiber[Fiber];
        end

        subgraph B3 [Basic Components]
            Log[Log];
            Config[Config];
            Thread[Thread];
            Socket;
            Stream;
            DB[Database];
        end
    end

    B --> C[System/3rd-party Libraries];

    subgraph C
        direction LR
        Linux_Kernel[Linux Kernel - epoll/socket];
        Pthread;
        OpenSSL;
        Boost;
        YAML_CPP[yaml-cpp];
        Zookeeper;
    end

    A -- Business Logic --> B1;
    B1 -- Dependency --> B2;
    B2 -- Dependency --> B3;
    B3 -- Dependency --> C;
```

**架构说明:**

1.  **业务应用层 (Business Application Layer)**: 开发者基于Sylar框架编写的具体业务逻辑，例如Web服务、API接口、游戏服务器等。
2.  **Sylar 核心功能模块 (Sylar Core Modules)**:
    *   **协议与应用 (Protocols & Applications)**: 提供了HTTP、WebSocket等具体的应用层协议实现，以及服务发现等分布式组件。
    *   **核心调度与封装 (Core Scheduling & Encapsulation)**: 这是Sylar的精髓所在，包括了协程的实现、调度模型以及将同步IO转换为异步的Hook机制。
    *   **基础组件 (Basic Components)**: 提供了日志、配置、线程、Socket、数据库访问等开发中不可或缺的基础工具。
3.  **系统底层/第三方库 (System & 3rd-party Libraries)**: Sylar构建于操作系统和一系列优秀的开源库之上，例如使用epoll进行IO多路复用，使用OpenSSL进行加密通信等。

这样的分层架构使得框架的各个部分职责清晰，耦合度低，便于维护和扩展。


---

## 2. 核心基础模块

本章节将详细介绍Sylar框架提供的核心基础功能模块，它们是构建上层应用和服务的基石。

### 2.1 日志模块 (`sylar::log`)

日志模块是Sylar框架中最基础、最重要的组件之一。它提供了一个高性能、线程安全、可灵活配置的日志系统，能够满足服务器开发的复杂需求。

#### 2.1.1 核心概念

Sylar的日志系统主要由以下几个核心概念组成：

*   **Logger (日志器)**: 日志的直接使用者和管理者。每个Logger实例拥有一个唯一的名称（如 "root", "system"），并关联一个日志级别。只有当日志事件的级别高于或等于Logger的级别时，该日志才会被记录。
*   **LogAppender (日志输出地)**: 定义了日志的最终输出位置。一个Logger可以关联一个或多个Appender。框架内置了两种Appender：
    *   `StdoutLogAppender`: 输出到标准控制台。
    *   `FileLogAppender`: 输出到指定文件。
*   **LogFormatter (日志格式器)**: 定义了每条日志消息的文本格式。每个Appender都可以拥有一个独立的Formatter；如果未指定，则使用其所属Logger的Formatter。
*   **LogEvent (日志事件)**: 代表一个独立的日志行为。当代码中触发一次日志记录时，框架会创建一个LogEvent对象，其中包含了日志级别、代码位置（文件名、行号）、线程ID、协程ID、时间戳以及日志消息本身等所有上下文信息。
*   **LoggerManager (日志器管理器)**: 一个单例类，负责创建、管理和查找项目中的所有Logger实例。

它们之间的关系如下图所示：

```mermaid
graph TD
    subgraph LoggerManager
        L1[Logger root]
        L2[Logger system]
    end

    subgraph Appenders
        A1[StdoutAppender]
        A2[FileAppender root.txt]
        A3[FileAppender system.txt]
    end
    
    subgraph Formatters
        F1[Formatter A]
        F2[Formatter B]
    end

    L1 -- owns --> A1
    L1 -- owns --> A2
    L2 -- owns --> A3
    
    A1 -- uses --> F1
    A2 -- uses --> F1
    A3 -- uses --> F2

    LogEvent -- sent to --> L1
    LogEvent -- sent to --> L2
```

#### 2.1.2 使用方式

框架推荐使用宏来记录日志，既方便又高效。

**1. 获取Logger实例**

```cpp
// 获取主日志器 root
sylar::Logger::ptr g_logger = SYLAR_LOG_ROOT();

// 获取名为 "system" 的日志器，如果不存在则会自动创建
sylar::Logger::ptr system_log = SYLAR_LOG_NAME("system");
```

**2. 记录日志**

支持两种风格的日志记录：

*   **流式风格 (Stream Style)**: 类似于 `std::cout`，使用 `<<` 操作符。

    ```cpp
    SYLAR_LOG_INFO(g_logger) << "This is an info log.";
    SYLAR_LOG_DEBUG(g_logger) << "User login success, username=" << "sylar" << ", uid=" << 100;
    ```

*   **格式化风格 (Format Style)**: 类似于 `printf`，使用占位符。

    ```cpp
    SYLAR_LOG_FMT_ERROR(g_logger, "This is an error log, error_code=%d", 500);
    SYLAR_LOG_FMT_FATAL(g_logger, "System crash, reason: %s", "memory leak");
    ```

#### 2.1.3 配置说明

日志系统的所有行为都通过YAML文件进行配置。默认情况下，程序会加载 `conf/log.yml`。

一个典型的日志配置如下：

```yaml
# bin/conf/log.yml
logs:
    # 定义一个名为 root 的日志器
    - name: root
      # 日志级别：只有 >= info 的日志才会被记录
      level: info
      # 该日志器关联的 Appender 列表
      appenders:
          # 第一个 Appender：输出到文件
          - type: FileLogAppender
            file: /logs/sylar/root.txt
            # 这个Appender自身的级别，可以不指定，不指定则使用logger的level
            level: info 
          # 第二个 Appender：输出到控制台
          - type: StdoutLogAppender

    # 定义一个名为 system 的日志器
    - name: system
      level: debug
      # 自定义该日志器的格式
      formatter: '%d{%Y-%m-%d %H:%M:%S}%T[%p]%T[%c]%T%m%n'
      appenders:
          - type: FileLogAppender
            file: /logs/sylar/system.txt
```

**配置项说明:**

*   `logs`: 顶层键，其值是一个列表，列表中的每一项定义一个Logger。
*   `name`: 日志器的名称，必须唯一。
*   `level`: 日志器的全局级别，可选值为 `debug`, `info`, `warn`, `error`, `fatal` (不区分大小写)。
*   `formatter`: (可选) 该日志器的默认格式化模板。
*   `appenders`: 该日志器拥有的Appender列表。
    *   `type`: Appender的类型，目前支持 `FileLogAppender` 和 `StdoutLogAppender`。
    *   `file`: (仅对`FileLogAppender`有效) 日志文件的路径。
    *   `level`: (可选) Appender自身的级别，可以覆盖Logger的级别。
    *   `formatter`: (可选) Appender自身的格式，可以覆盖Logger的格式。

#### 2.1.4 格式化模板

通过组合不同的占位符，可以实现灵活的日志格式定义。

| 占位符 | 描述                               |
| :--- | :--------------------------------- |
| `%m` | 日志消息体 (Message)               |
| `%p` | 日志级别 (Level)                   |
| `%r` | 程序启动后的累计毫秒数 (Elapse)    |
| `%c` | 日志器名称 (Logger Name)           |
| `%t` | 线程ID (Thread Id)                 |
| `%n` | 换行符 (New Line)                  |
| `%d` | 日期时间 (Date Time)，可带格式，如 `%d{%Y-%m-%d %H:%M:%S}` |
| `%f` | 文件名 (File Name)                 |
| `%l` | 行号 (Line Number)                 |
| `%T` | 制表符 (Tab)                       |
| `%F` | 协程ID (Fiber Id)                  |
| `%N` | 线程名称 (Thread Name)             |

**默认格式:** `%d{%Y-%m-%d %H:%M:%S}%T%t%T%N%T%F%T[%p]%T[%c]%T%f:%l%T%m%n`


### 2.2 配置模块 (`sylar::config`)

配置模块是Sylar框架实现"约定优于配置"思想的核心。它允许开发者在代码中直接定义配置项，框架会自动从YAML文件中加载相应的值，并支持在运行时动态更新。

#### 2.2.1 核心概念

*   **ConfigVar (配置变量)**: 这是一个模板类 `ConfigVar<T>`，用于包装一个具体类型的配置项。程序中所有可配置的参数都应该是一个 `ConfigVar<T>` 的全局实例。
*   **Config (配置管理器)**: 这是一个单例类，提供了静态方法来管理所有的 `ConfigVar` 实例。其中最重要的功能是 `Lookup`，用于定义和查找配置项。
*   **LexicalCast (类型转换器)**: 框架的核心魔法之一。它负责将YAML文件中的字符串形式的值，转换为 `ConfigVar<T>` 中指定的 `T` 类型。框架已经内置了对所有基本类型和常用STL容器的转换支持。

#### 2.2.2 使用方式

**1. 定义和声明配置项**

在代码的任何位置（通常是全局范围），使用 `Config::Lookup` 来定义一个配置项。

```cpp
// 定义一个整型的端口配置，名称为 "system.port"，默认值为 8080
sylar::ConfigVar<int>::ptr g_tcp_port = 
    sylar::Config::Lookup("system.port", (int)8080, "system port");

// 定义一个字符串向量的配置
sylar::ConfigVar<std::vector<std::string>>::ptr g_allow_ips = 
    sylar::Config::Lookup("system.allow_ips", std::vector<std::string>{"127.0.0.1"}, "allow ips");
```

**2. 在代码中使用配置项**

通过 `getValue()` 方法来获取配置项的当前值。

```cpp
void start_server() {
    int port = g_tcp_port->getValue();
    std::vector<std::string> ips = g_allow_ips->getValue();
    
    // ... 使用 port 和 ips 启动服务
}
```

当配置文件被重新加载后，再次调用 `g_tcp_port->getValue()` 将会自动获取到最新的值。

#### 2.2.3 配置文件格式

配置模块使用YAML格式。配置项的名称通过 `.` 来映射YAML的层级结构。

对应于上一节代码的YAML文件内容如下：

```yaml
# conf/server.yml
system:
    port: 9999
    allow_ips:
        - ************
        - ************
```

程序启动时，`g_tcp_port` 的值会自动从默认的 `8080` 更新为 `9999`。

#### 2.2.4 高级功能

**1. 支持自定义类型**

除了内置类型，配置模块还支持自定义的复杂类型。要使其工作，开发者需要为自定义类特化 `sylar::LexicalCast` 模板。

例如，有一个 `Person` 类：
```cpp
class Person {
public:
    std::string name;
    int age;
};
```

需要提供 `string` 到 `Person` 以及 `Person` 到 `string` 的双向转换：
```cpp
namespace sylar {

// string -> Person
template<>
class LexicalCast<std::string, Person> {
public:
    Person operator()(const std::string& v) {
        YAML::Node node = YAML::Load(v);
        Person p;
        p.name = node["name"].as<std::string>();
        p.age = node["age"].as<int>();
        return p;
    }
};

// Person -> string
template<>
class LexicalCast<Person, std::string> {
public:
    std::string operator()(const Person& p) {
        YAML::Node node;
        node["name"] = p.name;
        node["age"] = p.age;
        std::stringstream ss;
        ss << node;
        return ss.str();
    }
};

}
```
完成特化后，就可以像内置类型一样定义 `Person` 类型的配置项了：
```cpp
sylar::ConfigVar<Person>::ptr g_person =
    sylar::Config::Lookup("class.person", Person(), "system person");
```
对应的YAML配置：
```yaml
class:
    person:
        name: sylar
        age: 30
```

**2. 变更通知**

可以为一个配置项添加监听器，当其值发生改变时，自动执行回调函数。

```cpp
g_tcp_port->addListener([](const int& old_value, const int& new_value){
    SYLAR_LOG_INFO(SYLAR_LOG_ROOT()) << "tcp port changed from " << old_value 
                                     << " to " << new_value;
    // 在这里可以执行重启服务等操作
});
```
这个特性对于实现服务的动态配置、热更新等功能至关重要。


### 2.3 线程与同步模块 (`sylar::thread`, `sylar::mutex`)

为了充分利用多核CPU的性能，现代服务器开发离不开多线程编程。Sylar框架在 `pthread` 的基础上，提供了一套更易用、功能更丰富的线程和同步原语封装。

#### 2.3.1 线程封装 (`sylar::Thread`)

框架通过 `sylar::Thread` 类对线程的创建、销毁和管理进行了封装。

**核心特性:**

*   **函数式接口**: 构造函数直接接收一个 `std::function<void()>` 对象，可以是普通函数、成员函数、Lambda表达式等，作为线程的执行体。
*   **线程命名**: 在创建线程时可以指定一个唯一的名称，该名称会通过 `pthread_setname_np` 设置到内核中，并在日志和调试工具中显示，极大地方便了问题排查。
*   **线程局部存储**: 提供了静态方法 `Thread::GetThis()` 和 `Thread::GetName()` 来快速获取当前线程的 `Thread` 对象指针和名称。

**使用示例:**

```cpp
#include "sylar/thread.h"
#include "sylar/log.h"

void thread_task() {
    SYLAR_LOG_INFO(SYLAR_LOG_ROOT()) << "hello from new thread, name=" 
                                     << sylar::Thread::GetName();
}

int main() {
    // 创建一个新线程，并命名为 "worker_thread"
    sylar::Thread::ptr thr(new sylar::Thread(&thread_task, "worker_thread"));

    // 等待线程执行完毕
    thr->join();
    
    return 0;
}
```

#### 2.3.2 同步原语

Sylar提供了一系列同步原语的封装，全部位于 `sylar/mutex.h` 中。相比于C++11标准库，Sylar提供了更丰富的锁类型以应对不同的并发场景。

**1. RAII风格的锁管理**

Sylar的一个核心设计哲学是使用RAII（资源获取即初始化）来管理锁的生命周期。每一种锁都提供了一个内嵌的 `Lock` 类型（或 `ReadLock`/`WriteLock`），在构造时自动加锁，在析构时自动解锁。这可以从根本上杜绝忘记释放锁而导致的死锁问题。

```cpp
sylar::Mutex s_mutex;
int g_count = 0;

void safe_increment() {
    // lock 在构造时自动加锁
    sylar::Mutex::Lock lock(s_mutex); 
    g_count++;
} // 函数结束时，lock 析构，自动解锁
```

**2. 锁的种类**

| 类名 | `Lock` 类型 | 描述 | 适用场景 |
| :--- | :--- | :--- | :--- |
| `sylar::Mutex` | `Mutex::Lock` | 互斥锁。最常用的锁，封装了 `pthread_mutex_t`。 | 保护临界区，保证同一时间只有一个线程访问。 |
| `sylar::RWMutex` | `RWMutex::ReadLock`<br>`RWMutex::WriteLock` | 读写锁。封装了 `pthread_rwlock_t`。允许多个读线程同时访问，但写线程会独占。 | 读多写少的场景，可以显著提高并发性能。 |
| `sylar::Spinlock` | `Spinlock::Lock` | 自旋锁。封装了 `pthread_spinlock_t`。当获取锁失败时，线程不会被挂起，而是在CPU上空转等待。 | 锁的持有时间极短，且线程不希望被上下文切换的场景。可以避免线程切换带来的开销。 |
| `sylar::CASLock` | `CASLock::Lock` | 原子锁。基于C++11的 `std::atomic_flag` 实现，是一种更轻量级的自旋锁。 | 类似于自旋锁，用于极高频、极短时间的临界区保护。 |

**3. 信号量 (`sylar::Semaphore`)**

`Semaphore` 是对POSIX信号量 `sem_t` 的封装，用于控制能够同时访问某一特定资源的线程数量。

**使用示例:**

```cpp
// 初始化一个值为5的信号量，表示最多允许5个线程同时执行某个任务
sylar::Semaphore s_sem(5);

void limited_task() {
    s_sem.wait(); // 获取信号量，如果计数器为0则阻塞等待
    
    // ... 执行受限的任务 ...
    
    s_sem.notify(); // 释放信号量，计数器加1
}
```


---

## 3. 协程与调度模块

协程是Sylar框架的灵魂，是其高性能高并发能力的核心。Sylar实现了一套完整的N:M协程模型，即 M 个协程可以在 N 个内核线程上进行调度，并配合Hook机制，使得开发者可以用同步的方式编写异步的高性能代码。

### 3.1 协程 (`sylar::Fiber`)

协程（Fiber）可以理解为一种用户态的轻量级线程。与操作系统线程相比，它的创建、销毁和切换完全在用户态完成，无需陷入内核，因此开销极小，可以轻松创建数十万甚至上百万个。

#### 3.1.1 核心概念

*   **上下文 (Context)**: 每个协程都拥有自己独立的CPU寄存器状态和栈空间。上下文切换的本质就是保存当前协程的寄存器状态，并加载目标协程的寄存器状态到CPU。Sylar使用 `ucontext.h` 库中的 `getcontext`, `setcontext`, `makecontext`, `swapcontext` 等函数来实现上下文管理。
*   **主协程 (Main Fiber)**: 每个线程都拥有一个主协程，它不执行具体业务逻辑，而是作为该线程上所有子协程的"调度中心"或"宿主"。子协程通过与主协程进行 `swapcontext` 来实现切入和切出。
*   **子协程 (Sub-Fiber)**: 开发者创建的、用于执行具体业务逻辑的协程。每个子协程都有一个绑定的执行函数（`std::function<void()>`）。
*   **协程状态机**: 每个协程在其生命周期内会经历不同的状态，调度器根据协程的状态来决定下一步的操作。
    *   `INIT`: 初始化状态，协程刚被创建。
    *   `READY`: 就绪状态，表示协程可以被执行。
    -   `EXEC`: 执行中状态。
    *   `HOLD`: 挂起状态，协程主动让出执行权，等待被再次换入。
    *   `TERM`: 终止状态，协程的执行函数已经正常返回。
    *   `EXCEPT`: 异常状态，协程的执行函数抛出了未捕获的异常。

#### 3.1.2 协程的生命周期

一个典型的协程生命周期如下：

```mermaid
graph TD
    subgraph ThreadA
        direction LR
        MainFiberA[Main Fiber]
        subgraph SubFiberF1
            direction TB
            F1_INIT[INIT] --> F1_EXEC{EXEC};
            F1_EXEC --> F1_HOLD[HOLD];
            F1_HOLD --> F1_EXEC;
            F1_EXEC --> F1_TERM[TERM];
        end
    end

    Start --> F1_INIT;
    MainFiberA -- swapIn --> F1_EXEC;
    F1_EXEC -- YieldToHold --> MainFiberA;
    MainFiberA -- swapIn --> F1_EXEC;
    F1_EXEC -- return --> MainFiberA;
```

1.  **创建**: `Fiber::ptr fiber(new Fiber(task_func));` 创建一个子协程，此时其状态为 `INIT`。
2.  **切入执行**: 在主协程中调用 `fiber->swapIn()`，当前上下文被保存到主协程的 `ucontext_t` 中，同时加载子协程的上下文，程序执行流跳转到 `task_func`。子协程状态变为 `EXEC`。
3.  **主动让出 (Yield)**: 在 `task_func` 中调用 `Fiber::YieldToHold()`，当前上下文被保存到子协程的 `ucontext_t` 中，同时加载主协程的上下文，程序执行流返回到主协程 `swapIn()` 的下一行。子协程状态变为 `HOLD`。
4.  **再次切入**: 主协程可以再次调用 `fiber->swapIn()` 来恢复该子协程的执行。
5.  **执行完毕**: `task_func` 执行完毕后，子协程内部会自动调用 `swapOut()` 切回主协程，其状态变为 `TERM`。一个处于 `TERM` 状态的协程不能再被 `swapIn`。

#### 3.1.3 使用示例

下面的示例展示了协程的基本创建和切换过程。

```cpp
#include "sylar/sylar.h"

sylar::Logger::ptr g_logger = SYLAR_LOG_ROOT();

void run_in_fiber() {
    SYLAR_LOG_INFO(g_logger) << "run_in_fiber begin";
    // 协程主动让出执行权，切换回主协程
    sylar::Fiber::YieldToHold();
    SYLAR_LOG_INFO(g_logger) << "run_in_fiber end";
}

void test_fiber() {
    SYLAR_LOG_INFO(g_logger) << "main begin";
    
    // 1. 创建子协程
    sylar::Fiber::ptr fiber(new sylar::Fiber(run_in_fiber));
    
    // 2. 第一次切入，执行 run_in_fiber 的第一部分
    fiber->swapIn(); 
    
    SYLAR_LOG_INFO(g_logger) << "main after first swapIn";
    
    // 3. 第二次切入，执行 run_in_fiber 的第二部分
    fiber->swapIn();
    
    SYLAR_LOG_INFO(g_logger) << "main end";
}

int main() {
    test_fiber();
    return 0;
}
```

**预期输出:**

```
main begin
run_in_fiber begin
main after first swapIn
run_in_fiber end
main end
```

> **注意**: 上述代码只是为了演示协程的基本原理。在实际开发中，开发者通常不会直接操作 `Fiber` 对象，而是通过更高层的**调度器 (Scheduler)** 来管理和调度协程。


### 3.2 调度器 (`sylar::Scheduler` & `sylar::IOManager`)

如果说协程是Sylar的基本作战单位，那么调度器就是运筹帷幄的指挥中心。它负责将成千上万的协程任务高效、合理地分配给底层的内核线程去执行。Sylar提供了两种调度器：

*   **`Scheduler`**: 通用的协程调度器，实现了基本的N:M协程调度模型。
*   **`IOManager`**: `Scheduler` 的子类，在通用调度的基础上，集成了基于 `epoll` 的IO事件监控和定时器功能，是Sylar中进行网络编程的默认调度器。

#### 3.2.1 通用调度器 `Scheduler`

`Scheduler` 内部维护了一个线程池和一个全局的任务队列。

**核心流程:**

1.  **启动**: 创建 `Scheduler` 对象后，调用 `start()` 方法，此时调度器会创建指定数量的内核线程。
2.  **线程工作循环**: 每个被创建的线程都会执行 `Scheduler::run()` 方法，进入核心的调度循环。
3.  **任务分发**: 外部通过调用 `schedule(task)` 方法向调度器中添加任务（一个待执行的协程或函数）。任务会被放入一个全局的任务队列中。
4.  **任务执行**: 调度循环中的线程会不断地从全局任务队列中取出任务，并通过 `swapIn()` 切入执行。
5.  **`idle`状态**: 如果任务队列为空，线程不会退出，而是会执行一个特殊的 `idle` 协程，该协程会不断检查调度器是否可以停止，并在无事可做时主动让出（`YieldToHold`），避免CPU空转。

```mermaid
graph TD
    subgraph Scheduler
        direction LR
        
        subgraph ThreadPool
            T1[Thread 1]
            T2[Thread 2]
            T3[Thread ...]
        end

        TQ[Global Task Queue]
    end

    User[User Code] -- schedule a task --> TQ

    T1 -- fetch task from --> TQ
    T2 -- fetch task from --> TQ
    T3 -- fetch task from --> TQ

    T1 -- execute --> Fiber1[Fiber 1]
    T2 -- execute --> Fiber2[Fiber 2]
    T3 -- execute --> Fiber3[Fiber 3]
```

#### 3.2.2 IO调度器 `IOManager`

`IOManager` 是Sylar的精华所在，它在 `Scheduler` 的基础上，通过 `epoll` 将IO事件与协程调度无缝结合。

**核心架构:**

`IOManager` 继承了 `Scheduler`，因此它本身就是一个协程调度器。它的独特之处在于其 `idle` 协程的实现。

*   当 `IOManager` 的任务队列为空时，工作线程会切入 `idle` 协程。
*   `idle` 协程的核心不再是简单的 `Yield`，而是调用 `epoll_wait()`，将线程阻塞在IO事件的等待上。
*   `epoll_wait` 的 `timeout` 参数由 `IOManager` 内部的定时器模块 (`TimerManager`) 来管理，它等于最近一个将要触发的定时器的时间。

**事件驱动流程:**

1.  一个协程发起了一个非阻塞IO操作（例如 `connect`），但该操作无法立即完成。
2.  通过 `IOManager::addEvent()`，将当前协程和这个IO句柄（`fd`）的某个事件（如 `READ` 或 `WRITE`）关联起来，并注册到 `epoll` 中。
3.  当前协程调用 `Fiber::YieldToHold()` 主动让出执行权，被挂起。
4.  工作线程继续执行其他就绪的协程，或者在无事可做时切入 `idle` 协程，阻塞在 `epoll_wait` 上。
5.  当之前注册的IO事件发生时（例如 `connect` 成功，`fd` 变为可写），`epoll_wait` 返回。
6.  `idle` 协程被唤醒，它遍历所有就绪的 `fd`，找到对应的事件上下文。
7.  `idle` 协程将之前挂起的协程（在第3步中）重新放入 `Scheduler` 的任务队列中，使其状态变为 `READY`。
8.  工作线程在下一次调度循环中，就会执行这个已经就绪的协程，从它被挂起的地方继续执行。

这个流程完美地将**异步的IO事件**转换为了**同步的协程执行流**，开发者无需关心底层的 `epoll` 和回调函数，只需像编写同步代码一样处理IO即可。

```mermaid
sequenceDiagram
    participant C as Fiber
    participant IOM as IOManager
    participant E as epoll
    participant K as Kernel

    C->>IOM: Initiate non-blocking IO (e.g., connect)
    Note right of C: IO cannot be completed immediately
    IOM->>E: addEvent(fd, WRITE, C)
    C->>IOM: YieldToHold()
    Note right of C: Fiber C is suspended
    IOM->>E: epoll_wait(timeout)
    Note right of IOM: Scheduler thread is blocked
    K-->>E: IO is ready (e.g., connect success)
    E-->>IOM: epoll_wait() returns
    IOM->>IOM: Iterate through ready events, find Fiber C
    IOM->>IOM: schedule(C)
    Note right of IOM: Fiber C becomes READY
    IOM->>C: swapIn()
    Note right of C: Fiber C is resumed, continues execution
```

#### 3.2.3 定时器

`IOManager` 同时也是一个定时器管理器。

*   **添加定时器**: 通过 `addTimer` 或 `addConditionTimer` 添加一次性或周期性的定时任务。
*   **实现原理**: `IOManager` 内部维护一个按执行时间排序的定时器列表。每次进入 `idle` 协程调用 `epoll_wait` 时，都会将 `timeout` 参数设置为最近一个定时器的触发时间。
*   **触发**:
    *   如果 `epoll_wait` 是因为超时而返回，`IOManager` 会检查所有到期的定时器，并将其回调函数作为新的任务添加到调度队列中。
    *   如果 `epoll_wait` 是因为IO事件而返回，`IOManager` 同样会检查是否有定时器在此期间到期，并进行处理。


### 3.3 Hook模块 (`sylar::hook`)

Hook模块是Sylar框架的"黑魔法"，它使得开发者可以用完全同步的编码风格，来编写具有异步性能的高并发网络程序。

#### 3.3.1 实现原理

Hook模块的原理基于Linux动态链接器的符号解析机制。

1.  **符号劫持 (Symbol Interception)**: 在程序启动时，Sylar通过 `dlsym` 函数找到并保存了libc中一系列IO相关的原始函数地址（如 `read`, `connect` 等）。然后，Sylar自己实现了一套与原始函数同名、同参数、同返回值的函数。当用户代码调用 `read()` 时，链接器会优先链接到Sylar实现的 `read` 函数，而不是libc中的版本，这就完成了"劫持"。
2.  **协程化改造**: Sylar实现的 `read` 函数并不是真的去执行阻塞的读操作。它的逻辑如下：
    a. 首先，它会尝试以**非阻塞**模式调用一次原始的 `read_f` 函数。
    b. 如果数据已准备好，能够立即读到，则直接返回，整个过程没有发生协程切换，性能极高。
    c. 如果数据未准备好（`errno == EAGAIN`），说明当前操作会阻塞。此时，它不会傻等，而是调用 `IOManager::addEvent()` 将当前协程注册到该文件描述符（fd）的 `READ` 事件上。
    d. 注册完毕后，立即调用 `Fiber::YieldToHold()`，将当前协程挂起，让出CPU执行权给其他就绪的协程。
    e. 当底层socket真的有数据可读时，`IOManager` 的 `epoll_wait` 会被唤醒，并把之前挂起的协程重新调度为 `READY` 状态。
    f. 当该协程再次被执行时，它会从 `YieldToHold()` 的地方恢复，并再次尝试调用原始的 `read_f` 函数，此时数据已经备好，可以成功读取并返回。

整个过程对于用户代码来说是完全透明的。开发者只是调用了一个看似阻塞的 `read()` 函数，但其底层已经被Sylar替换为基于事件通知和协程调度的异步操作。

```mermaid
sequenceDiagram
    participant App as Application Code (Fiber A)
    participant Hook as Hook::read
    participant IOM as IOManager
    participant Kernel as Kernel

    App->>Hook: read(fd, buf, len)
    Note right of App: Call a seemingly synchronous function
    Hook->>Kernel: non-blocking read(fd)
    Kernel-->>Hook: Return EAGAIN (data not ready)
    Hook->>IOM: addEvent(fd, READ, Fiber A)
    Hook->>App: YieldToHold()
    Note right of App: Fiber A is suspended, CPU executes other fibers
    Kernel-->>IOM: Data is ready on fd (epoll triggered)
    IOM-->>Hook: Resume execution of Fiber A
    Hook->>Kernel: non-blocking read(fd)
    Kernel-->>Hook: Return read data
    Hook-->>App: Return data
    Note right of App: read() call completed
```

#### 3.3.2 使用方式

Hook模块的使用是**隐式**且**自动**的。

*   **自动开启**: 当你创建并启动一个 `IOManager` 或 `Scheduler` 时，在其工作线程的 `run()` 方法中，会自动调用 `set_hook_enable(true)` 来为当前线程开启Hook功能。
*   **透明使用**: 开启Hook后，开发者无需修改任何代码，直接使用标准的POSIX IO函数即可。

```cpp
void socket_client_task() {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    
    // connect 会被Hook，自动转换为异步connect
    // 如果无法立即连接，当前协程会在此处被挂起，而不是阻塞整个线程
    connect(sock, ...); 
    
    // send 会被Hook，自动转换为异步send
    send(sock, ...);
    
    char buf[1024];
    // recv 会被Hook，自动转换为异步recv
    // 如果没有数据可读，当前协程会在此处被挂起
    recv(sock, buf, sizeof(buf), 0);
    
    close(sock);
}

int main() {
    sylar::IOManager iom;
    // 将任务调度到IOManager中，该任务所在的线程会自动开启Hook
    iom.schedule(socket_client_task); 
    return 0;
}
```

#### 3.3.3 被Hook的函数列表

Sylar主要Hook了以下几类函数：

*   **`sleep` 系列**: `sleep`, `usleep`, `nanosleep`。会被转换为 `IOManager` 的定时器任务。
*   **`socket` 系列**: `socket`, `connect`, `accept`。
*   **`read` 系列**: `read`, `readv`, `recv`, `recvfrom`, `recvmsg`。
*   **`write` 系列**: `write`, `writev`, `send`, `sendto`, `sendmsg`。
*   **`close`**: `close`。
*   **其他**: `fcntl`, `ioctl`, `getsockopt`,